<template>
  <div class="work-order-page">
    <div class="list-action-wrapper">
      <QuickSearchTabbar :tabs="tabsData" @tab-click="handleTabClick" />
      <div class="search-row">
        <div>
          <SecondaryGroupingSelector :items="groupingItems" @change="handleGroupingChange" />
        </div>
        <div>
          <AdvancedSearchInput
            v-model="searchQuery"
            :search-options="searchOptions"
            @search="handleSearch"
            @type-change="handleSearchTypeChange"
          />
        </div>
      </div>
    </div>
    <div class="list-table-wrapper">
      <div class="list-table-header">
        <div class="list-table-header__left">
          <a-space size="middle">
            <a-button type="primary" :icon="h(PlusCircleFilled)" @click="createWorkerOrder"
              >创建工单</a-button
            >
            <a-button type="default" :icon="h(UploadOutlined)">导出</a-button>
            <a-button type="default" :icon="h(DownloadOutlined)">导入</a-button>
            <ColumnConfig :columns="columnSettings" @update="handleColumnsUpdate" />
            <a-button type="text" :icon="h(SortAscendingOutlined)">排序</a-button>
            <a-button type="text" :icon="h(FilterOutlined)">筛选</a-button>
            <a-button type="text" :icon="h(ColumnHeightOutlined)">行高</a-button>
          </a-space>
        </div>
        <div class="list-table-header__right">
          <a-space>
            <a-button type="text" class="custom-btn">
              <svg-icon icon-name="icon-log" color="#000" />
              <span>导入日志</span>
            </a-button>
            <a-button type="text" class="custom-btn">
              <svg-icon icon-name="icon-jilu" color="#000" />
              <span>操作记录</span>
            </a-button>
          </a-space>
        </div>
      </div>
      <div class="list-table-body">
        <a-table
          :dataSource="dataSource"
          :columns="visibleColumns"
          :pagination="{
            showTotal: (total: number) => `${total} 条记录`,
            showSizeChanger: true,
            showQuickJumper: true,
          }"
          :customRow="setRowStyle"
          :scroll="{ x: 4200, y: 400 }"
        >
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'progressBar'">
              <ProgressBar size="small" :progress-list="step" />
            </template>
            <template v-if="column.key === 'action'">
              <a-button type="link">编辑</a-button>
              <a-button type="link">查看</a-button>
              <a-button type="link">操作</a-button>
            </template>
          </template>
        </a-table>
      </div>
    </div>
  </div>
  <WorkerOrderItem :open="open" @close="onClose" />
</template>

<script setup lang="ts">
import { ref, computed, h } from 'vue'

import {
  ProfileFilled,
  PlusCircleFilled,
  UploadOutlined,
  DownloadOutlined,
  SettingOutlined,
  FilterOutlined,
  SortAscendingOutlined,
  ColumnHeightOutlined,
} from '@ant-design/icons-vue'

// 快速搜索相关
import QuickSearchTabbar from '@/components/QuickSearchTabbar.vue'
// 定义标签数据
const tabsData = ref([
  { name: '全部', icon: ProfileFilled, active: true },
  { name: '看板1', icon: ProfileFilled, active: false },
  { name: '看板2', icon: ProfileFilled, active: false },
])
// 事件处理函数
const handleTabClick = (tab: any, index: number) => {
  tabsData.value.forEach((item, i) => {
    item.active = i === index
  })
  console.log('Tab clicked:', tab, index)
}

// 二级分组选择器
import SecondaryGroupingSelector from '@/components/SecondaryGroupingSelector.vue'
// 定义分组数据
const groupingItems = ref([
  { label: '全部', value: 'all' },
  { label: '压铸', value: 'casting' },
  { label: '去毛刺', value: 'deburring' },
  { label: '车', value: 'turning' },
  { label: '钻', value: 'drilling' },
  { label: '铣', value: 'milling' },
  { label: '氧化', value: 'oxidation' },
  { label: '喷涂', value: 'painting' },
  { label: '包装入库', value: 'packaging' },
])
const handleGroupingChange = (value: any) => {
  console.log('Grouping changed:', value)
}

// 高级搜索输入框
import AdvancedSearchInput from '@/components/AdvancedSearchInput.vue'
// 搜索相关数据
const searchQuery = ref('')
const searchOptions = ref([
  {
    label: '工单编号、产品编号、产品名称、产品规格',
    value: 'all',
    placeholder: '输入产品编号搜索',
  },
  { label: '工单编号', value: 'workOrderNo', placeholder: '输入工单编号搜索' },
  { label: '产品编号', value: 'productNo', placeholder: '输入产品编号搜索' },
  { label: '产品名称', value: 'productName', placeholder: '输入产品名称搜索' },
  { label: '产品规格', value: 'productSpec', placeholder: '输入产品规格搜索' },
  { label: '工单备注', value: 'workOrderNote', placeholder: '输入工单备注搜索' },
  { label: '紧急程度', value: 'urgency', placeholder: '输入紧急程度搜索' },
  { label: '压铸机', value: 'machine', placeholder: '输入压铸机搜索' },
])
const handleSearchTypeChange = (type: string) => {
  console.log('Search type changed:', type)
}
const handleSearch = (data: { type: string; value: string }) => {
  console.log('Search triggered:', data)
  // 这里可以调用API进行搜索
}

// 表格相关
import { tableColumns } from './tableColumns'
import type { TableColumn } from '@/common/types'
import ColumnConfig from '@/components/ColumnConfig/index.vue'
import { mockData } from './mockData'
import ProgressBar, { type ProgressItem } from '@/components/ProgressBar/index.vue'
const dataSource = ref(mockData)
const columnSettings = ref<TableColumn[]>(tableColumns)

const setRowStyle = () => {
  return {
    style: {
      height: '64px',
    },
  }
}
// 计算可见列
const visibleColumns = computed(() =>
  columnSettings.value
    .filter((col) => col.visible)
    .map((col) => ({
      ...col,
      fixed: col.fixed === false ? undefined : col.fixed,
    })),
)
// 处理列配置更新
const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  columnSettings.value = newColumns
}
const step = ref<ProgressItem[]>([
  { status: 'wait', percent: 0, stepName: '压铸' },
  { status: 'process', percent: 12, stepName: '去毛刺' },
  { status: 'process', percent: 48, stepName: '车' },
  { status: 'success', percent: 102, stepName: '钻' },
  { status: 'success', percent: 100, stepName: '铣' },
  { status: 'wait', percent: 8, stepName: '氧化' },
  { status: 'wait', percent: 1, stepName: '喷涂' },
])
// 创建工单
import WorkerOrderItem from './components/worker-order-item/worker-order-item.vue'

const open = ref<boolean>(false)

const createWorkerOrder = () => {
  open.value = true
}

const onClose = () => {
  open.value = false
}
</script>

<style src="./index.scss" scoped></style>
