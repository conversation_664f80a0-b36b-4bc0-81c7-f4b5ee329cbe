<template>
  <a-drawer
    :open="props.open"
    :closable="false"
    :headerStyle="{ padding: '0 24px' }"
    :body-style="{ padding: 0 }"
    :width="drawerWidth"
    @close="handleClose"
  >
    <template #title>
      <div class="drawer-header">
        <div class="drawer-header-title">创建工单</div>
        <div class="drawer-header-tabs">
          <a-tabs v-model:activeKey="activeTab">
            <a-tab-pane key="base" tab="基本信息" />
            <a-tab-pane key="custom" tab="自定义信息" />
            <a-tab-pane key="task" tab="生产任务" />
            <a-tab-pane key="bom" tab="用料清单" />
          </a-tabs>
        </div>
        <div class="drawer-header-btn">
          <a-tooltip>
            <template #title>{{ isMaximize ? '收起' : '全屏' }}</template>
            <a-button
              class="drawer-header-btn-item"
              type="text"
              :icon="h(isMaximize ? ShrinkOutlined : ArrowsAltOutlined)"
              @click="handleMaximize"
            />
          </a-tooltip>
          <a-divider type="vertical" />
          <a-button
            class="drawer-header-btn-item"
            type="text"
            :icon="h(CloseOutlined)"
            @click="handleClose"
          />
        </div>
      </div>
    </template>
    <!-- 内容区域 -->
    <div class="drawer-content">
      <div class="drawer-content-item">
        <div class="drawer-content-item__title">基本信息</div>
        <div class="drawer-content-item__content">
          <a-form layout="vertical" :model="form">
            <a-row :gutter="16">
              <a-col :span="8">
                <a-form-item label="工单编号">
                  <a-input v-model:value="form.orderNo" placeholder="请输入,忽略将自动生成" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="计划时间" required>
                  <template #tooltip>
                    <div class="plan-time">剩余{{ getRemainingTime(form.planTime[1]) }}</div>
                  </template>
                  <a-range-picker
                    style="width: 100%"
                    v-model:value="form.planTime"
                    :show-time="{ format: 'HH:mm' }"
                    format="YYYY-MM-DD HH:mm"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="状态">
                  <a-select v-model:value="form.status" placeholder="请选择状态" />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="产品信息" required>
                  <template #tooltip>
                    <div class="product-info">
                      <span class="product-info-btn">
                        <PlusOutlined style="margin-right: 4px" /> 创建
                      </span>
                      <a-divider type="vertical" />
                      <span class="product-info-btn">
                        <SearchOutlined style="margin-right: 4px" /> 高级选择
                      </span>
                    </div>
                  </template>
                  <a-select v-model:value="form.productId" placeholder="请选择状态" />
                  <template #extra>
                    <div class="product-detail detail-box">
                      <div class="info-block product-detail__left">
                        <div class="product-row">
                          <span class="product-row__label">产品编号</span>
                          <span class="product-row__value">CP20230004</span>
                        </div>
                        <div class="product-row">
                          <span class="product-row__label">产品名称</span>
                          <span class="product-row__value">G031</span>
                        </div>
                        <div class="product-row">
                          <span class="product-row__label">产品规格</span>
                          <span class="product-row__value">1250T</span>
                        </div>
                      </div>
                      <div class="info-block product-detail__right">
                        <div class="stock-amount">{{ formatNumber(1000) }}</div>
                        <div class="stock-unit-text">库存数量(<span>只</span>)</div>
                      </div>
                    </div>
                  </template>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="计划数" required>
                  <a-input-number style="width: 100%" v-model:value="form.planCount" />
                  <template #extra>
                    <div class="detail-box plan-count-extra">
                      <div class="info-block plan-count__item">
                        <div class="amount good">{{ formatNumber(1000) }}</div>
                        <div class="label">良品数</div>
                      </div>
                      <div class="info-block plan-count__item">
                        <div class="amount bad">{{ formatNumber(1000) }}</div>
                        <div class="label">不良品数</div>
                      </div>
                      <div class="info-block plan-count__item">
                        <div class="amount rate">{{ formatNumber(1000) }}</div>
                        <div class="label">不良品率</div>
                      </div>
                    </div>
                  </template>
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="备注">
                  <a-textarea
                    v-model:value="form.remark"
                    :autoSize="{ minRows: 5, maxRows: 5 }"
                    placeholder="请输入"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="8">
                <a-form-item label="关联单据">
                  <a-input v-model:value="form.relatedOrder" disabled />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>
      <div class="drawer-content-item">
        <div class="drawer-content-item__title">自定义信息</div>
        <div class="drawer-content-item__content"></div>
      </div>
      <div class="drawer-content-item">
        <div class="drawer-content-item__title">生产任务</div>
        <div class="drawer-content-item__content">
          <a-space style="margin-bottom: 12px">
            <a-button type="primary" :icon="h(PlusCircleFilled)">添加任务</a-button>
            <a-button type="default" :icon="h(SaveOutlined)">保存工艺路线</a-button>
            <ColumnConfig
              :columns="taskColumnsSettings"
              @update="handleColumnsUpdate($event, 'task')"
            />
            <a-button type="text" :icon="h(ColumnHeightOutlined)">行高</a-button>
          </a-space>
          <a-table
            :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
            :scroll="{ x: 3200, y: 600 }"
            :columns="taskColumns"
            :data-source="data"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-space size="small">
                  <a>编辑</a>
                  <a>复制</a>
                  <a>删除</a>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
      <div class="drawer-content-item">
        <div class="drawer-content-item__title">用料清单</div>
        <div class="drawer-content-item__content">
          <a-space style="margin-bottom: 12px">
            <a-button type="primary" :icon="h(PlusCircleFilled)">添加用料</a-button>
            <a-button type="default" :icon="h(FileTextOutlined)">按BOM添加</a-button>
            <a-button type="default" :icon="h(SaveOutlined)">保存物料清单</a-button>
            <a-button type="default" :icon="h(ReloadOutlined)">更新物料清单</a-button>
            <ColumnConfig
              :columns="materialColumnsSettings"
              @update="handleColumnsUpdate($event, 'material')"
            />
            <a-button type="text" :icon="h(ColumnHeightOutlined)">行高</a-button>
          </a-space>
          <a-table
            :row-selection="{ selectedRowKeys: state.selectedRowKeys, onChange: onSelectChange }"
            :scroll="{ x: 3200, y: 600 }"
            :columns="materialColumns"
            :data-source="materialData"
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.key === 'action'">
                <a-space size="small">
                  <a>编辑</a>
                  <a>复制</a>
                  <a>删除</a>
                </a-space>
              </template>
            </template>
          </a-table>
        </div>
      </div>
    </div>
    <template #footer>
      <div class="drawer-footer">
        <a-button class="drawer-footer__btn" type="default" @click="handleClose">取消</a-button>
        <a-button class="drawer-footer__btn" type="primary" @click="handleCreate">创建</a-button>
      </div>
    </template>
  </a-drawer>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, h, reactive } from 'vue'
import {
  PlusCircleFilled,
  SaveOutlined,
  CloseOutlined,
  ArrowsAltOutlined,
  ShrinkOutlined,
  ColumnHeightOutlined,
  PlusOutlined,
  SearchOutlined,
  FileTextOutlined,
  ReloadOutlined,
} from '@ant-design/icons-vue'
import dayjs from 'dayjs'

import ColumnConfig from '@/components/ColumnConfig/index.vue'

import { formatNumber, getRemainingTime } from '@/utils'
import { taskColumns, materialColumns } from './columns'
import { mockData, materialData } from './mockData'

const props = defineProps<{
  open: boolean
}>()
const emit = defineEmits(['update:open', 'close'])

const drawerWidth = ref<string>('1536px')
const isMaximize = ref<boolean>(false)
const activeTab = ref('base')

const form = ref({
  orderNo: '',
  planTime: [dayjs().startOf('day'), dayjs().endOf('day')],
  status: '',
  productId: '',
  planCount: undefined,
  remark: '',
  relatedOrder: '',
})
const data = ref(mockData)
const state = reactive<{
  selectedRowKeys: string[] | number[]
  loading: boolean
}>({
  selectedRowKeys: [], // Check here to configure the default column
  loading: false,
})

const taskColumnsSettings = ref<any[]>(taskColumns)
const materialColumnsSettings = ref<any[]>(materialColumns)

const onSelectChange = (selectedRowKeys: string[] | number[]) => {
  console.log('selectedRowKeys changed: ', selectedRowKeys)
  state.selectedRowKeys = selectedRowKeys
}

const handleMaximize = () => {
  isMaximize.value = !isMaximize.value
  if (isMaximize.value) {
    drawerWidth.value = '100%'
  } else {
    drawerWidth.value = '1536px'
  }
}

const handleClose = () => {
  emit('close')
}

const handleCreate = () => {
  const { planTime, ...rest } = form.value
  const params = {
    ...rest,
    planTime: planTime.map((item) => item.format('YYYY-MM-DD HH:mm')),
  }
  console.log(params)
}

const handleColumnsUpdate = (newColumns: any[], type: 'task' | 'material') => {
  if (type === 'task') taskColumnsSettings.value = newColumns
  if (type === 'material') materialColumnsSettings.value = newColumns
}
</script>

<style src="./index.scss" scoped></style>
