<template>
  <a-dropdown
    placement="bottomLeft"
    trigger="click"
    :overlayStyle="{ padding: 0 }"
    v-model:open="dropdownVisible"
  >
    <a-button type="text" :icon="h(SettingOutlined)">字段配置</a-button>
    <template #overlay>
      <div class="column-config-dropdown" @click.stop>
        <ColumnConfigPanel :columns="internalColumns" @update="handleColumnsUpdate" />
      </div>
    </template>
  </a-dropdown>
</template>

<script setup lang="ts">
import { ref, watch, h } from 'vue'
import { SettingOutlined } from '@ant-design/icons-vue'
import ColumnConfigPanel from '@/components/ColumnConfigPanel/index.vue'
import type { TableColumn } from '@/common/types'

interface Props {
  columns: TableColumn[]
}
interface Emits {
  (e: 'update', columns: TableColumn[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 内部列配置状态
const internalColumns = ref<TableColumn[]>([...props.columns])

// 下拉菜单显示状态
const dropdownVisible = ref<boolean>(false)

// 处理列配置更新
const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  internalColumns.value = newColumns
  emit('update', newColumns)
}

// 监听 props.columns 变化，同步到内部状态
watch(
  () => props.columns,
  (newColumns) => {
    internalColumns.value = [...newColumns]
  },
  { deep: true },
)
</script>

<style lang="scss" scoped>
// 字段配置下拉面板样式
.column-config-dropdown {
  padding: 0;

  :deep(.ant-dropdown-menu) {
    padding: 0;
    box-shadow: none;
  }
}

// 确保下拉菜单不会因为内部点击而关闭
:deep(.ant-dropdown) {
  .ant-dropdown-menu {
    .ant-dropdown-menu-item {
      padding: 0;
    }
  }
}
</style>
