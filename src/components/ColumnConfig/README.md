# ColumnConfig 组件

一个封装了字段配置功能的下拉菜单组件，包含 a-dropdown 和 ColumnConfigPanel 的完整配置组件。

## 功能特性

- 🎯 **完整封装**：包含下拉菜单触发器和字段配置面板
- 🔄 **双向绑定**：支持 v-model 控制下拉菜单显示状态
- 📋 **字段管理**：支持字段显示/隐藏、拖拽排序、固定列等功能
- 🎨 **样式保持**：保持原有的所有样式和交互效果
- 🔧 **类型安全**：完整的 TypeScript 类型支持

## Props

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `columns` | `TableColumn[]` | - | 表格列配置数组，必传 |
| `modelValue` | `boolean` | `false` | 控制下拉菜单显示状态，支持 v-model |

### TableColumn 类型定义

```typescript
interface TableColumn {
  title: string                    // 列标题
  dataIndex?: string              // 数据字段名
  key: string                     // 唯一标识
  width?: number                  // 列宽度
  visible: boolean                // 是否显示
  fixed?: boolean | 'left' | 'right'  // 是否固定
  order: number                   // 排序序号
  icon?: string                   // 字段图标
}
```

## Events

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `update:modelValue` | `(value: boolean)` | 下拉菜单显示状态变化时触发 |
| `update` | `(columns: TableColumn[])` | 字段配置更新时触发，返回修改后的列配置 |

## 基本用法

```vue
<template>
  <div>
    <!-- 基本使用 -->
    <ColumnConfig 
      :columns="columnSettings" 
      @update="handleColumnsUpdate" 
    />
    
    <!-- 使用 v-model 控制显示状态 -->
    <ColumnConfig 
      v-model="columnConfigVisible"
      :columns="columnSettings" 
      @update="handleColumnsUpdate" 
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ColumnConfig from '@/components/ColumnConfig/index.vue'
import type { TableColumn } from '@/views/produce/pages/worker-order/tableColumns'

const columnConfigVisible = ref(false)
const columnSettings = ref<TableColumn[]>([
  {
    title: '工单编号',
    dataIndex: 'workOrderNo',
    key: 'workOrderNo',
    visible: true,
    width: 120,
    fixed: 'left',
    order: 1,
    icon: 'file-text',
  },
  // ... 更多列配置
])

const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  columnSettings.value = newColumns
  // 这里可以添加其他逻辑，比如保存到本地存储
}
</script>
```

## 替换现有代码

将现有的 a-dropdown + ColumnConfigPanel 组合替换为此组件：

### 替换前
```vue
<a-dropdown
  placement="bottomLeft"
  trigger="click"
  :overlayStyle="{ padding: 0 }"
  v-model:open="columnConfigVisible"
>
  <a-button type="text" :icon="h(SettingOutlined)">字段配置</a-button>
  <template #overlay>
    <div class="column-config-dropdown" @click.stop>
      <ColumnConfigPanel :columns="columnSettings" @update="handleColumnsUpdate" />
    </div>
  </template>
</a-dropdown>
```

### 替换后
```vue
<ColumnConfig 
  v-model="columnConfigVisible"
  :columns="columnSettings" 
  @update="handleColumnsUpdate" 
/>
```

## 注意事项

1. **类型导入**：确保正确导入 `TableColumn` 类型
2. **样式保持**：组件内部已包含所有必要的样式，无需额外引入
3. **事件处理**：`@update` 事件会在任何字段配置变化时触发
4. **状态同步**：组件会自动同步 props.columns 的变化到内部状态

## 依赖

- Vue 3
- Ant Design Vue
- @ant-design/icons-vue
- ColumnConfigPanel 组件
- TableColumn 类型定义
